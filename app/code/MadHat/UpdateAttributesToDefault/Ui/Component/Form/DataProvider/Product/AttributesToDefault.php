<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace MadHat\UpdateAttributesToDefault\Ui\Component\Form\DataProvider\Product;

use Magento\Ui\DataProvider\AbstractDataProvider;
use Magento\Catalog\Model\ResourceModel\Product\Attribute\CollectionFactory as AttributeCollectionFactory;
use Magento\Catalog\Api\Data\ProductAttributeInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Eav\Model\Config as EavConfig;

class AttributesToDefault extends AbstractDataProvider
{
    /**
     * @var AttributeCollectionFactory
     */
    protected $attributeCollectionFactory;

    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * @var EavConfig
     */
    protected $eavConfig;

    /**
     * @var array
     */
    protected $loadedData;

    /**
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param AttributeCollectionFactory $attributeCollectionFactory
     * @param RequestInterface $request
     * @param EavConfig $eavConfig
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        AttributeCollectionFactory $attributeCollectionFactory,
        RequestInterface $request,
        EavConfig $eavConfig,
        array $meta = [],
        array $data = []
    ) {
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->attributeCollectionFactory = $attributeCollectionFactory;
        $this->request = $request;
        $this->eavConfig = $eavConfig;
        $this->collection = $this->attributeCollectionFactory->create(); // Not strictly used for rows, but good to have
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        // This DataProvider is primarily for providing the list of attributes to the form,
        // not for loading data of a specific entity.
        // The 'data' part might be used to pre-fill selections if needed, but for now, it's empty.
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }
        $this->loadedData = [];
        // We pass selected product IDs to the save controller, not really needed for form rendering here.
        // $items = $this->request->getParam($this->requestFieldName, []);
        // $this->loadedData[null]['product_ids'] = $items; // Example if we needed to pass it

        return $this->loadedData;
    }

    /**
     * Get meta
     *
     * @return array
     */
    public function getMeta()
    {
        error_log("MadHat DataProvider getMeta() called");
        try {
            $meta = parent::getMeta();
            $meta['attributes_details']['children'] = $this->getAttributeFields();
            error_log("MadHat DataProvider getMeta() completed successfully");
            return $meta;
        } catch (\Exception $e) {
            error_log("MadHat DataProvider getMeta() error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get attribute fields for the form
     *
     * @return array
     */
    protected function getAttributeFields()
    {
        error_log("MadHat getAttributeFields() called");
        try {
            $fields = [];
            $attributeCollection = $this->attributeCollectionFactory->create()
            ->addVisibleFilter()
            // ->addFieldToFilter(ProductAttributeInterface::IS_USER_DEFINED, 1) // Optionally filter user-defined
            ->addFieldToFilter(ProductAttributeInterface::FRONTEND_INPUT, ['nin' => ['media_image', 'gallery', 'image']]) // Exclude image types more broadly
            // ->addFieldToFilter(ProductAttributeInterface::IS_GLOBAL, \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_GLOBAL) // This would be to select only global
            // ->addFieldToFilter(ProductAttributeInterface::IS_GLOBAL, ['neq' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_GLOBAL]) // To select non-global
            ->setOrder(ProductAttributeInterface::FRONTEND_LABEL, 'ASC');

        // Attributes that can have "Use Default Value" are typically not global.
        // We want attributes that are not global OR global ones where resetting to null/false makes sense.
        // The Save controller logic handles setting to 'false' which for non-global attributes means "use default".
        // For global attributes, setting to 'false' or 'null' will literally change the global value.
        // The user should be aware of this. This form doesn't distinguish them yet.

        // Similar to Magento_Catalog/Controller/Adminhtml/Product/Action/Attribute/Save.php for attribute loading logic
        // We want attributes that can be updated via mass action.
        // Magento's "Update Attributes" mass action allows almost all attributes.
        // We'll try to mimic that, excluding some system/complex ones.

        $attributesToExclude = [
            'sku', 'url_key', 'url_path', 'image', 'small_image', 'thumbnail', 'media_gallery',
            'quantity_and_stock_status', 'category_ids', 'gift_message_available',
            'custom_options', 'product_has_weight', // System or complex attributes
            'tier_price', 'options_container', 'visibility', 'tax_class_id', 'type_id', 'status',
            'news_from_date', 'news_to_date', 'special_from_date', 'special_to_date', // Often have special handling
            'country_of_manufacture' // Example, might need more
        ];


        foreach ($attributeCollection as $attribute) {
            /** @var ProductAttributeInterface $attribute */
            if (in_array($attribute->getAttributeCode(), $attributesToExclude)) {
                continue;
            }
            // Ensure attribute can be applied to products (though addVisibleFilter should handle most of this)
            // if (!$attribute->getIsGlobal() && !$attribute->getApplyTo()) {
            //    continue;
            // }


            $fields[$attribute->getAttributeCode()] = [
                'arguments' => [
                    'data' => [
                        'config' => [
                            'label' => __('%1', $attribute->getStoreLabel()),
                            'dataType' => 'boolean',
                            'formElement' => 'checkbox',
                            'componentType' => 'field',
                            'prefer' => 'toggle',
                            'valueMap' => [
                                'true' => '1',
                                'false' => '0',
                            ],
                            'default' => '0', // Default to unchecked
                            'notice' => __('Set %1 to default value.', $attribute->getStoreLabel()),
                            'dataScope' => 'attributes_to_reset.' . $attribute->getAttributeCode(), // This will group them under an array
                            'sortOrder' => (int)$attribute->getPosition() ?: 100, // Basic sorting
                        ],
                    ],
                ],
            ];
        }
        error_log("MadHat getAttributeFields() completed, found " . count($fields) . " fields");
        return $fields;
        } catch (\Exception $e) {
            error_log("MadHat getAttributeFields() error: " . $e->getMessage());
            throw $e;
        }
    }
}
