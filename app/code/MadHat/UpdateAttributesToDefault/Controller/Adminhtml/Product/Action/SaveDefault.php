<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace MadHat\UpdateAttributesToDefault\Controller\Adminhtml\Product\Action;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Ui\Component\MassAction\Filter;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Catalog\Model\Product\Action as ProductAction;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Store\Model\Store;

class SaveDefault extends Action implements HttpPostActionInterface
{
    /**
     * Authorization level of a basic admin session
     *
     * @see _isAllowed()
     */
    const ADMIN_RESOURCE = 'MadHat_UpdateAttributesToDefault::action_update_default';

    /**
     * Check if form key is valid
     * @return bool
     */
    public function _processUrlKeys()
    {
        return true; // Temporarily disable form key validation for debugging
    }

    /**
     * @var Filter
     */
    protected $filter;

    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @var ProductAction
     */
    protected $productAction;

    /**
     * @var EavConfig
     */
    protected $eavConfig;

    /**
     * @param Context $context
     * @param Filter $filter
     * @param CollectionFactory $collectionFactory
     * @param ProductAction $productAction
     * @param EavConfig $eavConfig
     */
    public function __construct(
        Context $context,
        Filter $filter,
        CollectionFactory $collectionFactory,
        ProductAction $productAction,
        EavConfig $eavConfig
    ) {
        parent::__construct($context);
        $this->filter = $filter;
        $this->collectionFactory = $collectionFactory;
        $this->productAction = $productAction;
        $this->eavConfig = $eavConfig;
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        // Add debugging
        error_log("MadHat SaveDefault controller reached");

        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $formRequest = $this->getRequest()->getParam('attributes_to_reset', []);
        $storeId = $this->getRequest()->getParam('store', Store::DEFAULT_STORE_ID);

        // Debug log the request data
        error_log("Form request data: " . print_r($formRequest, true));
        error_log("All request params: " . print_r($this->getRequest()->getParams(), true));

        if (empty($formRequest)) {
            $this->messageManager->addErrorMessage(__('Please select at least one attribute to update.'));
            return $resultRedirect->setPath('catalog/product/');
        }

        $attributesToReset = array_keys(array_filter($formRequest)); // Get attribute codes where checkbox was checked

        if (empty($attributesToReset)) {
            $this->messageManager->addErrorMessage(__('No attributes were marked for reset.'));
            return $resultRedirect->setPath('catalog/product/');
        }

        // Get product IDs - try multiple sources
        $productIds = [];

        // First try to get from session (stored by EditDefault controller)
        $productIds = $this->_session->getData('madhat_selected_product_ids', []);

        // If not in session, try to get from request parameters
        if (empty($productIds)) {
            $productIds = $this->getRequest()->getParam('selected', []);
        }

        // If still empty, try the filter component as fallback
        if (empty($productIds)) {
            try {
                $collection = $this->filter->getCollection($this->collectionFactory->create());
                $productIds = $collection->getAllIds();
            } catch (\Exception $e) {
                // Filter component failed, continue with empty array
            }
        }

        if (empty($productIds)) {
            $this->messageManager->addErrorMessage(__('No products selected or matched the filter criteria.'));
            return $resultRedirect->setPath('catalog/product/');
        }

        try {
            // Prepare update data - use false to trigger "Use Default Value"
            $updateData = [];
            foreach($attributesToReset as $code){
                $updateData[$code] = false; // Using false to trigger "Use Default Value"
            }

            $this->productAction->updateAttributes($productIds, $updateData, $storeId);

            // Clear the session data
            $this->_session->unsetData('madhat_selected_product_ids');

            $this->messageManager->addSuccessMessage(
                __('A total of %1 product(s) attributes have been scheduled to be updated to default.', count($productIds))
            );
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        } catch (\Exception $e) {
            $this->messageManager->addExceptionMessage($e, __('Something went wrong while updating the product(s) attributes.'));
        }

        return $resultRedirect->setPath('catalog/product/');
    }
}
