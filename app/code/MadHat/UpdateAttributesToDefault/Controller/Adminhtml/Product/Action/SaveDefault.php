<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace MadHat\UpdateAttributesToDefault\Controller\Adminhtml\Product\Action;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Ui\Component\MassAction\Filter;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Catalog\Model\Product\Action as ProductAction;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Store\Model\Store;

class SaveDefault extends Action implements HttpPostActionInterface
{
    /**
     * Authorization level of a basic admin session
     *
     * @see _isAllowed()
     */
    const ADMIN_RESOURCE = 'MadHat_UpdateAttributesToDefault::action_update_default';

    /**
     * @var Filter
     */
    protected $filter;

    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @var ProductAction
     */
    protected $productAction;

    /**
     * @var EavConfig
     */
    protected $eavConfig;

    /**
     * @param Context $context
     * @param Filter $filter
     * @param CollectionFactory $collectionFactory
     * @param ProductAction $productAction
     * @param EavConfig $eavConfig
     */
    public function __construct(
        Context $context,
        Filter $filter,
        CollectionFactory $collectionFactory,
        ProductAction $productAction,
        EavConfig $eavConfig
    ) {
        parent::__construct($context);
        $this->filter = $filter;
        $this->collectionFactory = $collectionFactory;
        $this->productAction = $productAction;
        $this->eavConfig = $eavConfig;
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $formRequest = $this->getRequest()->getParam('attributes_to_reset', []);
        $storeId = $this->getRequest()->getParam('store', Store::DEFAULT_STORE_ID); // Get current store, or default to global

        if (empty($formRequest)) {
            $this->messageManager->addErrorMessage(__('Please select at least one attribute to update.'));
            return $resultRedirect->setPath('catalog/product/'); // Or back to the form?
        }

        $attributesToReset = array_keys(array_filter($formRequest)); // Get attribute codes where checkbox was checked

        if (empty($attributesToReset)) {
            $this->messageManager->addErrorMessage(__('No attributes were marked for reset.'));
            return $resultRedirect->setPath('catalog/product/');
        }

        // Get product IDs from the filter component (passed via UI component context)
        // The Filter component handles the logic of selected vs excluded IDs.
        try {
            $collection = $this->filter->getCollection($this->collectionFactory->create());
            $productIds = $collection->getAllIds();

            if (empty($productIds)) {
                $this->messageManager->addErrorMessage(__('No products selected or matched the filter criteria.'));
                return $resultRedirect->setPath('catalog/product/');
            }

            $attributesData = [];
            foreach ($attributesToReset as $attributeCode) {
                $attribute = $this->eavConfig->getAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode);
                if ($attribute && $attribute->getId()) {
                    // To reset to default, we set the value to null for a specific store view.
                    // If the attribute is global, this might behave differently or might not be what is expected.
                    // However, "Use Default Value" typically means clearing the store-specific value.
                    // For global attributes, there's no "Use Default Value" checkbox per se,
                    // as its value is the default. Changing it changes the only value.
                    // This action is primarily for store-view level attributes.
                    if ($attribute->isGlobal()) {
                        // For global attributes, setting to null might be destructive if there's no "parent" default.
                        // Magento's "Update attributes" sets it to a specific value.
                        // Resetting to default for a global attribute is trickier.
                        // For now, we will only process non-global attributes or assume admin knows the impact.
                        // A safer approach for global might be to skip or warn.
                        // For now, we'll allow it, as it's what "Use Default" implies (clearing current value).
                         $attributesData[$attributeCode] = null;
                    } else {
                        // For store view attributes, setting to null effectively means "use default".
                        $attributesData[$attributeCode] = null;
                    }
                }
            }

            if (empty($attributesData)) {
                $this->messageManager->addWarningMessage(__('None of the selected attributes could be processed or were global.'));
                return $resultRedirect->setPath('catalog/product/');
            }

            // Ensure storeId is correctly handled. For "Use Default", it's often Store::DEFAULT_STORE_ID
            // when saving, but the effect is on the current store view being edited, making it inherit.
            // Magento\Catalog\Model\Product\Action::updateAttributes($productIds, $attrData, $storeId)
            // If $storeId = 0 (Store::DEFAULT_STORE_ID), it updates the global scope.
            // If $storeId > 0, it updates the specific store view scope.
            // To make a store view use the default value, you must update its scope with a `null` or `false` value
            // (depending on attribute backend type, `false` is often used by Magento for "Use Default Value" checkboxes).
            // Let's try with `false` as it's more common for "Use Default Value" flags.

            $updateData = [];
            foreach($attributesToReset as $code){
                $updateData[$code] = false; // Using false to trigger "Use Default Value"
            }

            $this->productAction->updateAttributes($productIds, $updateData, $storeId);

            $this->messageManager->addSuccessMessage(
                __('A total of %1 product(s) attributes have been scheduled to be updated to default.', count($productIds))
            );
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        } catch (\Exception $e) {
            $this->messageManager->addExceptionMessage($e, __('Something went wrong while updating the product(s) attributes.'));
        }

        return $resultRedirect->setPath('catalog/product/');
    }
}
