<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace MadHat\UpdateAttributesToDefault\Controller\Adminhtml\Product\Action;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\ResultFactory;

class Test extends Action implements HttpGetActionInterface, HttpPostActionInterface
{
    /**
     * Authorization level of a basic admin session
     */
    const ADMIN_RESOURCE = 'MadHat_UpdateAttributesToDefault::action_update_default';

    public function execute()
    {
        error_log("=== MadHat Test controller reached ===");
        
        $result = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        $result->setData([
            'success' => true,
            'message' => 'Test controller works!',
            'method' => $this->getRequest()->getMethod(),
            'params' => $this->getRequest()->getParams()
        ]);
        
        return $result;
    }
    
    protected function _isAllowed()
    {
        return true; // Allow access for testing
    }
}
