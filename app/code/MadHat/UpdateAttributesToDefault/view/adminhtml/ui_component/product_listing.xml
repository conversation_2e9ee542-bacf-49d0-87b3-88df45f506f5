<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <listingToolbar name="listing_top">
        <massaction name="listing_massaction">
            <action name="madhat_update_attributes_to_default">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">madhat_update_attributes_to_default</item>
                        <item name="label" xsi:type="string" translate="true">MadHat Update attributes to default</item>
                        <item name="url" xsi:type="url" path="madhat_updateattributestodefault/product_action/editdefault"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">MadHat Update attributes to default</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to update selected attributes to default values?</item>
                        </item>
                         <item name="aclResource" xsi:type="string">MadHat_UpdateAttributesToDefault::action_update_default</item>
                    </item>
                </argument>
            </action>
        </massaction>
    </listingToolbar>
</listing>
